import { h } from 'vue'
import { cn } from '../utils'

const buttonVariants = {
  solid: 'bg-n-brand text-white hover:enabled:brightness-110 focus-visible:brightness-110',
  outline: 'border border-n-strong text-n-slate-11 hover:enabled:bg-n-alpha-2 focus-visible:bg-n-alpha-2',
  ghost: 'text-n-slate-12 hover:enabled:bg-n-alpha-2 focus-visible:bg-n-alpha-2',
  link: 'text-n-blue-text hover:enabled:underline focus-visible:underline'
}

const buttonSizes = {
  sm: 'h-8 px-3 text-sm',
  md: 'h-10 px-4 text-sm font-medium', 
  lg: 'h-12 px-5 text-base'
}

export const Button = {
  props: {
    variant: {
      type: String,
      default: 'solid',
      validator: (value) => Object.keys(buttonVariants).includes(value)
    },
    size: {
      type: String,
      default: 'md',
      validator: (value) => Object.keys(buttonSizes).includes(value)
    },
    disabled: <PERSON><PERSON><PERSON>,
    class: String
  },
  setup(props, { slots }) {
    return () => h('button', {
      class: cn(
        'inline-flex items-center justify-center min-w-0 gap-2 transition-all duration-200 ease-in-out rounded-lg outline-1 outline disabled:opacity-50',
        buttonVariants[props.variant],
        buttonSizes[props.size],
        props.class
      ),
      disabled: props.disabled
    }, slots.default?.())
  }
}