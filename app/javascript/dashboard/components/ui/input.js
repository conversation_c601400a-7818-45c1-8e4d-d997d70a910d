import { h } from 'vue'
import { cn } from '../utils'

export const Input = {
  props: {
    modelValue: [String, Number],
    placeholder: String,
    disabled: Boolean,
    class: String,
    type: {
      type: String,
      default: 'text'
    }
  },
  emits: ['update:modelValue'],
  setup(props, { emit }) {
    return () => h('input', {
      type: props.type,
      class: cn(
        'flex h-10 w-full rounded-md border border-n-strong bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',
        props.class
      ),
      placeholder: props.placeholder,
      disabled: props.disabled,
      value: props.modelValue,
      onInput: (e) => emit('update:modelValue', e.target.value)
    })
  }
}